function Item({ item, onEdit, onDelete }) {
  return (
    <li id={item?.id} className="todo_item">
      <button className="todo_items_left">
        <svg width="24" height="24" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" fillRule="nonzero" />
        </svg>
        <span>{item?.title}</span>
      </button>
      <div className="todo_items_right">
        <button onClick={() => onEdit(item)}>
          <span className="visually-hidden">Edit</span>
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M12.146 2.146a.5.5 0 0 1 .708 0l1 1a.5.5 0 0 1 0 .708l-8.5 8.5a.5.5 0 0 1-.168.11l-3 1a.5.5 0 0 1-.638-.638l1-3a.5.5 0 0 1 .11-.168l8.5-8.5z" />
          </svg>
        </button>
        <button onClick={() => onDelete(item)}>
          <span className="visually-hidden">Delete</span>
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M5.5 5.5a.5.5 0 0 1 .5.5v5a.5.5 0 0 1-1 0v-5a.5.5 0 0 1 .5-.5zm2.5.5a.5.5 0 0 0-1 0v5a.5.5 0 0 0 1 0v-5zm2 .5a.5.5 0 0 1 .5.5v5a.5.5 0 0 1-1 0v-5a.5.5 0 0 1 .5-.5zM14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h3.5a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z" />
          </svg>
        </button>
      </div>
    </li>
  );
}

function TODOList({ todos, onEdit, onDelete }) {
  return (
    <ol className="todo_list">
      {todos && todos.length > 0 ? (
        todos.map((item) => (
          <Item key={item.id} item={item} onEdit={onEdit} onDelete={onDelete} />
        ))
      ) : (
        <li>Seems lonely in here, what are you up to?</li>
      )}
    </ol>
  );
}

export default TODOList;