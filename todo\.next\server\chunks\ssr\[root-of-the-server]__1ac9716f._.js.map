{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/todo/todo/src/components/Form.jsx"], "sourcesContent": ["function Form() {\r\n  const handleSubmit = (event) => {\r\n    event.preventDefault();\r\n    // reset the form\r\n    event.target.reset();\r\n  };\r\n  return (\r\n    <form className=\"form\" onSubmit={handleSubmit}>\r\n      <label htmlFor=\"todo\">\r\n        <input\r\n          type=\"text\"\r\n          name=\"todo\"\r\n          id=\"todo\"\r\n          placeholder=\"Write your next task\"\r\n        />\r\n      </label>\r\n      <button>\r\n        <span className=\"visually-hidden\">Submit</span>\r\n        <svg>\r\n          <path d=\"\" />\r\n        </svg>\r\n      </button>\r\n    </form>\r\n  );\r\n}\r\nexport default Form;"], "names": [], "mappings": ";;;;;AAAA,SAAS;IACP,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc;QACpB,iBAAiB;QACjB,MAAM,MAAM,CAAC,KAAK;IACpB;IACA,qBACE,8OAAC;QAAK,WAAU;QAAO,UAAU;;0BAC/B,8OAAC;gBAAM,SAAQ;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,MAAK;oBACL,IAAG;oBACH,aAAY;;;;;;;;;;;0BAGhB,8OAAC;;kCACC,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;kCAClC,8OAAC;kCACC,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;AAKlB;uCACe", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/todo/todo/src/components/Header.jsx"], "sourcesContent": ["function Header() {\r\n  return (\r\n    <>\r\n      <svg>\r\n        <path d=\"\" /> \r\n      </svg>\r\n      <h1>TODO</h1>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;;AAAA,SAAS;IACP,qBACE;;0BACE,8OAAC;0BACC,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;0BAEV,8OAAC;0BAAG;;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/todo/todo/src/components/TODOHero.jsx"], "sourcesContent": ["function TODOHero({ todos_completed, total_todos }) {\r\n  return (\r\n    <section>\r\n      <div>\r\n        <p>Task Done</p>\r\n        <p>Keep it up</p>\r\n      </div>\r\n      <div>\r\n        {todos_completed}/{total_todos}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\nexport default TODOHero;"], "names": [], "mappings": ";;;;;AAAA,SAAS,SAAS,EAAE,eAAe,EAAE,WAAW,EAAE;IAChD,qBACE,8OAAC;;0BACC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;kCACH,8OAAC;kCAAE;;;;;;;;;;;;0BAEL,8OAAC;;oBACE;oBAAgB;oBAAE;;;;;;;;;;;;;AAI3B;uCACe", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/todo/todo/src/components/TODOList.jsx"], "sourcesContent": ["function Item({ item }) {\r\n  return (\r\n    <li id={item.id} className=\"todo_item\">\r\n      <button className=\"todo_items_left\">\r\n        <svg>\r\n          <circle cx=\"11.998\" cy=\"11.998\" fillRule=\"nonzero\" r=\"9.998\" />\r\n        </svg>\r\n        <p>{item.title}</p>\r\n      </button>\r\n      <div className=\"todo_items_right\">\r\n        <button>\r\n          <span className=\"visually-hidden\">Edit</span>\r\n          <svg>\r\n            <path d=\"\" />\r\n          </svg>\r\n        </button>\r\n        <button>\r\n          <span className=\"visually-hidden\">Delete</span>\r\n          <svg>\r\n            <path d=\"\" />\r\n          </svg>\r\n        </button>\r\n      </div>\r\n    </li>\r\n  );\r\n}\r\n\r\nfunction TODOList({ todos }) {\r\n  return (\r\n    <ol className=\"todo_list\">\r\n      {todos && todos.length > 0 ? (\r\n        todos.map((item, index) => <Item key={index} item={item} />)\r\n      ) : (\r\n        <p>Seems lonely in here, what are you up to?</p>\r\n      )}\r\n    </ol>\r\n  );\r\n}\r\n\r\nexport default TODOList;"], "names": [], "mappings": ";;;;;AAAA,SAAS,KAAK,EAAE,IAAI,EAAE;IACpB,qBACE,8OAAC;QAAG,IAAI,KAAK,EAAE;QAAE,WAAU;;0BACzB,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;kCACC,cAAA,8OAAC;4BAAO,IAAG;4BAAS,IAAG;4BAAS,UAAS;4BAAU,GAAE;;;;;;;;;;;kCAEvD,8OAAC;kCAAG,KAAK,KAAK;;;;;;;;;;;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;0CAClC,8OAAC;0CACC,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;kCAGZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;0CAAkB;;;;;;0CAClC,8OAAC;0CACC,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB;AAEA,SAAS,SAAS,EAAE,KAAK,EAAE;IACzB,qBACE,8OAAC;QAAG,WAAU;kBACX,SAAS,MAAM,MAAM,GAAG,IACvB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAAU,8OAAC;gBAAiB,MAAM;eAAb;;;;sCAEtC,8OAAC;sBAAE;;;;;;;;;;;AAIX;uCAEe", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/todo/todo/src/app/page.js"], "sourcesContent": ["import React from \"react\";\nimport Form from \"@/components/Form\";\nimport Header from \"@/components/Header\";\nimport TODOHero from \"@/components/TODOHero\";\nimport TODOList from \"@/components/TODOList\";\nfunction Home() {\n  return (\n    <div className=\"wrapper\">\n      <Header />\n      <TODOHero todos_completed={0} total_todos={0} />\n      <Form />\n      <TODOList todos={[]} />\n    </div>\n  );\n}\nexport default Home;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AACA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC,8HAAA,CAAA,UAAQ;gBAAC,iBAAiB;gBAAG,aAAa;;;;;;0BAC3C,8OAAC,0HAAA,CAAA,UAAI;;;;;0BACL,8OAAC,8HAAA,CAAA,UAAQ;gBAAC,OAAO,EAAE;;;;;;;;;;;;AAGzB;uCACe", "debugId": null}}]}